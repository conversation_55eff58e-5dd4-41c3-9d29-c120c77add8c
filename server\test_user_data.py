#!/usr/bin/env python3
"""
测试用户数据转换的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from core.database.database_manager import DatabaseManager
from apps.admin.services.user import UserService
from apps.admin.schemas.user import UserPageQuerySchema


async def test_user_data():
    """测试用户数据转换"""
    # 初始化数据库连接
    db_manager = DatabaseManager()
    await db_manager.init_database()
    
    session_generator = db_manager.get_async_session()
    session = await session_generator.__anext__()
    try:
        # 测试获取用户列表
        query_object = UserPageQuerySchema(pageNum=1, pageSize=10)
        data_scope_sql = "1 == 1"  # 简单的数据权限表达式
        
        try:
            result = await UserService.get_user_list_services(
                session, query_object, data_scope_sql, is_page=True
            )
            
            print("用户列表数据结构:")
            print(f"总数: {result.total}")
            print(f"行数: {len(result.rows)}")
            
            if result.rows:
                first_user = result.rows[0]
                print(f"第一个用户数据: {first_user}")
                print(f"用户ID字段: {first_user.get('userId')}")
                print(f"用户名字段: {first_user.get('userName')}")
                
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
    finally:
        await session.close()


if __name__ == "__main__":
    asyncio.run(test_user_data())
